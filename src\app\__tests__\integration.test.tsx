/**
 * Integration tests for the main application
 * Tests that all components work together properly
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Home from '../page';

// Mock the performance monitor
jest.mock('@/lib/performance-monitor', () => ({
  usePerformanceMonitor: () => ({
    metrics: { fps: 60, memory: 50 },
    measureInteraction: jest.fn((name, fn) => fn()),
    shouldRenderEffect: jest.fn(() => true),
  }),
}));

// Mock the accessibility hook
jest.mock('@/hooks/use-accessibility', () => ({
  useAccessibility: () => ({
    announce: jest.fn(),
  }),
}));

// Mock the enhanced interactions hook
jest.mock('@/hooks/use-enhanced-interactions', () => ({
  useEnhancedInteractions: () => ({
    handleSwipe: jest.fn(),
    handlePinch: jest.fn(),
  }),
}));

// Mock the navigation manager
jest.mock('@/lib/navigation', () => ({
  navigationManager: {
    initialize: jest.fn(() => ({ page: 'dashboard' })),
    navigateTo: jest.fn(),
    navigateBack: jest.fn(),
    subscribe: jest.fn(),
    unsubscribe: jest.fn(),
  },
  useNavigation: () => ({
    currentRoute: { page: 'dashboard' },
    navigateTo: jest.fn(),
    navigateBack: jest.fn(),
  }),
}));

// Mock the store
jest.mock('@/lib/store', () => ({
  useAppStore: () => ({
    currentPage: 'dashboard',
    setCurrentPage: jest.fn(),
  }),
}));

describe('Application Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should render the main application without errors', async () => {
    render(<Home />);
    
    // Wait for the app to initialize
    await waitFor(() => {
      expect(screen.getByTestId('layout-provider')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('should render the adaptive layout system', async () => {
    render(<Home />);
    
    await waitFor(() => {
      expect(screen.getByTestId('adaptive-layout')).toBeInTheDocument();
    });
  });

  it('should render the gesture navigation system', async () => {
    render(<Home />);
    
    await waitFor(() => {
      expect(screen.getByTestId('gesture-navigation')).toBeInTheDocument();
    });
  });

  it('should render the vertical layout system', async () => {
    render(<Home />);
    
    await waitFor(() => {
      expect(screen.getByTestId('vertical-layout')).toBeInTheDocument();
    });
  });

  it('should render welcome content on dashboard page', async () => {
    render(<Home />);
    
    await waitFor(() => {
      expect(screen.getByText('Welcome to Cobalt Mobile')).toBeInTheDocument();
    });
  });

  it('should render quick start guide', async () => {
    render(<Home />);
    
    await waitFor(() => {
      expect(screen.getByText('Quick Start Guide')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Connect Equipment:')).toBeInTheDocument();
    expect(screen.getByText('Plan Your Session:')).toBeInTheDocument();
    expect(screen.getByText('Monitor Progress:')).toBeInTheDocument();
  });

  it('should handle initialization errors gracefully', async () => {
    // Mock initialization failure
    const mockMeasureInteraction = jest.fn().mockRejectedValue(new Error('Init failed'));
    
    jest.doMock('@/lib/performance-monitor', () => ({
      usePerformanceMonitor: () => ({
        metrics: { fps: 60, memory: 50 },
        measureInteraction: mockMeasureInteraction,
        shouldRenderEffect: jest.fn(() => true),
      }),
    }));

    render(<Home />);
    
    // The app should still render even if initialization fails
    await waitFor(() => {
      const layoutProvider = screen.queryByTestId('layout-provider');
      const asyncState = screen.queryByTestId('async-state');
      expect(layoutProvider || asyncState).toBeInTheDocument();
    });
  });
});
